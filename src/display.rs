use crate::app::{AppMessage, MessageContent};
use crate::syntax_highlighting;
use crossterm::style::{Color, SetBackgroundColor, ResetColor, Stylize, Print};
use std::io::{self, stdout, Write};
use console::measure_text_width; // For measuring visible width of text with ANSI codes
use textwrap::{Options, WordSplitter};

pub fn print_formatted_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout(); // Use stdout for more control with crossterm
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24)); // Default width 80

    if app_msg.sender == "User" {
        // Start with a clean approach
        let min_box_width = 8;
        let actual_cols = if terminal_width < min_box_width { min_box_width } else { terminal_width };
        
        // Draw the top border - this defines the exact width
        let top_border = format!("╭{}╮", "─".repeat((actual_cols - 2) as usize));
        crossterm::execute!(out, Print(format!("{}\n", top_border)))?;

        // Get the text content
        let text_content = if let Some(MessageContent::Text(text)) = app_msg.parts.first() {
            text.lines().next().unwrap_or("").trim()
        } else {
            ""
        };

        // Format the text with color
        let colored_text = format!("{}", text_content.cyan());
        let visible_text_width = measure_text_width(&colored_text);
        
        // Calculate the exact content width (excluding borders)
        let content_width = actual_cols as usize - 2; // -2 for left and right borders
        
        // Fixed parts of the middle line
        let left_part = "│ > "; // Left border and prompt
        let left_part_width = measure_text_width(left_part);
        
        // Calculate how much space is available for text and padding
        let available_for_text_and_padding = content_width - left_part_width;
        
        // Truncate text if needed
        let display_text = if visible_text_width > available_for_text_and_padding {
            // We need to truncate
            let mut truncated = String::new();
            let mut current_width = 0;
            
            for c in text_content.chars() {
                let char_width = measure_text_width(&c.to_string());
                if current_width + char_width > available_for_text_and_padding {
                    break;
                }
                truncated.push(c);
                current_width += char_width;
            }
            
            truncated.cyan().to_string()
        } else {
            colored_text
        };
        
        // Recalculate the visible width after potential truncation
        let final_text_width = measure_text_width(&display_text);
        
        // Calculate padding to fill the remaining space exactly
        let padding_width = available_for_text_and_padding - final_text_width;
        let padding = " ".repeat(padding_width);
        
        // Construct and print the middle line
        let middle_line = format!("{}{}{} │", left_part, display_text, padding);
        crossterm::execute!(out, Print(format!("{}\n", middle_line)))?;
        
        // Draw the bottom border - exactly matching the top border width
        let bottom_border = format!("╰{}╯", "─".repeat((actual_cols - 2) as usize));
        crossterm::execute!(out, Print(format!("{}\n", bottom_border)))?;

    } else if app_msg.sender == "AI" {
        crossterm::execute!(out, Print("\n"))?; // Initial newline for AI message block

        // Add 2-space left margin for all AI messages
        let outer_left_margin = "  ";
        let outer_right_margin = "  ";
        // Calculate the width available for content *between* the outer margins
        let content_wrap_width = terminal_width
            .saturating_sub(outer_left_margin.len() as u16 + outer_right_margin.len() as u16);

        // Ensure content_wrap_width is at least 1 to avoid panic with textwrap
        let content_wrap_width = if content_wrap_width < 1 { 1 } else { content_wrap_width };

        for (idx, part) in app_msg.parts.iter().enumerate() {
            if idx > 0 {
                let prev_part = &app_msg.parts[idx - 1];
                let prev_ended_with_newline = match prev_part {
                    MessageContent::Text(t) => t.is_empty() || t.ends_with('\n'),
                    MessageContent::CodeBlock { .. } | MessageContent::ToolCall(_) => true,
                };
                if !prev_ended_with_newline {
                    crossterm::execute!(out, Print("\n"))?;
                }
            }

            match part {
                MessageContent::Text(text_content) => {
                    if text_content.is_empty() {
                        // If the original text content was just a newline or empty,
                        // ensure we print a blank line with margins, or skip if that's preferred.
                        // crossterm::execute!(out, Print(format!("{}{}{}\n", outer_left_margin, "", outer_right_margin)))?;
                    } else {
                        // Configure textwrap for plain text
                        // For plain text, AsciiSpace is usually fine.
                        // If your text might have CJK characters or complex scripts, consider other WordSeparator options.
                        let options = Options::new(content_wrap_width as usize)
                                            .word_separator(textwrap::WordSeparator::AsciiSpace)
                                            .subsequent_indent("") // We handle margins per line
                                            .initial_indent("");   // We handle margins per line

                        for line in text_content.lines() { // Process original lines one by one
                            if line.trim().is_empty() && !line.is_empty() { // Preserve lines that were just spaces
                                crossterm::execute!(out, Print(format!("{}{}{}\n", outer_left_margin, line, outer_right_margin)))?;
                                continue;
                            }
                            if line.is_empty() { // Preserve completely empty lines
                                crossterm::execute!(out, Print(format!("{}{}{}\n", outer_left_margin, "", outer_right_margin)))?;
                                continue;
                            }
                            let wrapped_lines = textwrap::wrap(line, &options);
                            for wrapped_line in wrapped_lines {
                                crossterm::execute!(out, Print(format!("{}{}{}\n", outer_left_margin, wrapped_line, outer_right_margin)))?;
                            }
                        }
                    }
                    // Add a newline after text blocks if they weren't empty, for consistent spacing
                    if !text_content.is_empty() {
                         crossterm::execute!(out, Print("\n"))?;
                    }
                }
                MessageContent::CodeBlock { language, content } => {
                    // IntelliJ background color
                    let code_block_bg_color = Color::Rgb {
                        r: 43,
                        g: 43,
                        b: 43,
                    };

                    // Top padding line for the code block (full width background)
                    crossterm::execute!(
                        out,
                        SetBackgroundColor(code_block_bg_color),
                        Print(" ".repeat(terminal_width as usize)),
                        Print("\n"), // Newline after the padding line
                        ResetColor // Reset immediately so only this line has this specific full background
                    )?;

                    let highlighted_code_full =
                        syntax_highlighting::highlight_code_to_ansi_string(content, language.as_deref());

                    // Margins INSIDE the code block's background
                    let inner_left_margin = "  "; // These are the required 2 spaces
                    let inner_right_margin = "  ";

                    // Width available for code *between* the inner_left_margin and inner_right_margin
                    let code_content_wrap_width = terminal_width
                        .saturating_sub(inner_left_margin.len() as u16 + inner_right_margin.len() as u16);
                    
                    // Ensure code_content_wrap_width is at least 1
                    let code_content_wrap_width = if code_content_wrap_width < 1 { 1 } else { code_content_wrap_width };

                    // Configure textwrap for ANSI-highlighted code
                    // We need special handling for ANSI escape codes
                    let options = Options::new(code_content_wrap_width as usize)
                        // Use a custom word splitter that preserves ANSI escape codes
                        .word_splitter(WordSplitter::Custom(|word| {
                            // Simple implementation that splits at reasonable points
                            // but avoids splitting inside ANSI escape sequences
                            let mut splits = Vec::new();
                            let mut in_ansi = false;
                            let mut last_split = 0;
                            
                            for (i, c) in word.char_indices() {
                                if c == '\x1B' { // ESC character
                                    in_ansi = true;
                                } else if in_ansi && c == 'm' {
                                    in_ansi = false;
                                } else if !in_ansi && (c == '_' || c == '-') {
                                    // Split after hyphens and underscores when not in ANSI sequence
                                    splits.push(i + 1);
                                    last_split = i + 1;
                                }
                            }
                            
                            // If the word is very long and we haven't found any good split points,
                            // add some artificial ones every 10 characters, but not in ANSI sequences
                            if splits.is_empty() && word.len() > 20 {
                                in_ansi = false;
                                for (i, c) in word.char_indices() {
                                    if c == '\x1B' { // ESC character
                                        in_ansi = true;
                                    } else if in_ansi && c == 'm' {
                                        in_ansi = false;
                                    } else if !in_ansi && i > last_split + 10 {
                                        splits.push(i);
                                        last_split = i;
                                    }
                                }
                            }
                            
                            splits
                        }))
                        .break_words(true); // Break long tokens if they don't fit

                    if content.is_empty() {
                        // Handle empty code block: print a line with inner margins and background
                        crossterm::execute!(
                            out,
                            SetBackgroundColor(code_block_bg_color),
                            Print(inner_left_margin),
                            Print(" ".repeat(code_content_wrap_width as usize)), // Fill the wrap width
                            Print(inner_right_margin),
                            ResetColor,
                            Print("\n")
                        )?;
                    } else {
                        for original_line in highlighted_code_full.lines() {
                            if original_line.is_empty() { // Handle empty lines within the code
                                crossterm::execute!(
                                    out,
                                    SetBackgroundColor(code_block_bg_color),
                                    Print(inner_left_margin),
                                    Print(" ".repeat(code_content_wrap_width as usize)), // Fill the wrap width
                                    Print(inner_right_margin),
                                    ResetColor,
                                    Print("\n")
                                )?;
                                continue;
                            }
                            let wrapped_segments = textwrap::wrap(original_line, &options);
                            for segment in wrapped_segments {
                                crossterm::execute!(out, SetBackgroundColor(code_block_bg_color))?;
                                crossterm::execute!(out, Print(inner_left_margin))?;
                                crossterm::execute!(out, Print(&segment))?; // segment already has ANSI
                                crossterm::execute!(out, Print(inner_right_margin))?;

                                // Calculate padding to fill the rest of the terminal line
                                let visible_width_of_segment_with_margins =
                                    inner_left_margin.len() +
                                    measure_text_width(&segment) + // measure_text_width is ANSI-aware
                                    inner_right_margin.len();
                                
                                let line_padding_len = terminal_width.saturating_sub(visible_width_of_segment_with_margins as u16);
                                if line_padding_len > 0 {
                                    crossterm::execute!(out, Print(" ".repeat(line_padding_len as usize)))?;
                                }
                                
                                crossterm::execute!(out, ResetColor, Print("\n"))?;
                            }
                        }
                    }

                    // Bottom padding line for the code block (full width background)
                    crossterm::execute!(
                        out,
                        SetBackgroundColor(code_block_bg_color),
                        Print(" ".repeat(terminal_width as usize)),
                        Print("\n"), // Newline after the padding line
                        ResetColor
                    )?;
                    
                    // Always add a newline after code blocks for better visual separation
                    crossterm::execute!(out, Print("\n"))?;
                }
                MessageContent::ToolCall(tool_call) => {
                    // Wrapping for tool calls can also be added if arguments can be very long.
                    let tool_header = format!("[TOOL CALL REQUESTED]: {}", tool_call.tool_name);
                    crossterm::execute!(
                        out,
                        Print(outer_left_margin),
                        Print(tool_header.yellow().bold()),
                        Print(outer_right_margin),
                        Print("\n")
                    )?;

                    match serde_json::to_string_pretty(&tool_call.arguments) {
                        Ok(args_json) => {
                            let options = Options::new(content_wrap_width as usize)
                                .word_separator(textwrap::WordSeparator::AsciiSpace);
                            for line_str in args_json.lines() {
                                let wrapped_lines = textwrap::wrap(line_str, &options);
                                for wrapped_line in wrapped_lines {
                                    crossterm::execute!(
                                        out,
                                        Print(outer_left_margin),
                                        Print("  "), // Extra indent for args
                                        Print(wrapped_line.yellow()),
                                        Print(outer_right_margin),
                                        Print("\n")
                                    )?;
                                }
                            }
                        }
                        Err(e) => {
                            let error_msg = format!("Error formatting args: {}", e);
                            crossterm::execute!(
                                out,
                                Print(outer_left_margin),
                                Print("  "), // Extra indent
                                Print(error_msg.red()),
                                Print(outer_right_margin),
                                Print("\n")
                            )?;
                        }
                    }
                    
                    // Always add a newline after tool calls for better visual separation
                    crossterm::execute!(out, Print("\n"))?;
                }
            }
        }
        // We already add newlines after each content type, so we don't need an extra one here
        // println!();

    } else { // System messages, Tool Execution, etc.
        // This part also might benefit from wrapping if text can be long.
        let sender_prefix_str = match app_msg.sender.as_str() {
            "System" | "Tool Execution" => format!("{}: ", app_msg.sender), // Apply styling later
            _ => format!("{}: ", app_msg.sender),
        };
        
        // Calculate the prefix length before styling (for indentation)
        let prefix_len = sender_prefix_str.len();
        
        let styled_sender_prefix = match app_msg.sender.as_str() {
            "System" | "Tool Execution" => sender_prefix_str.clone().magenta().bold(),
            _ => sender_prefix_str.clone().bold(),
        };

        let mut first_line_of_message = true;
        if app_msg.parts.is_empty() {
            crossterm::execute!(out, Print(format!("{}\n", styled_sender_prefix.to_string().trim_end())))?;
        } else {
            for part in &app_msg.parts {
                match part {
                    MessageContent::Text(text_content) => {
                        // Could also wrap this if it can be long
                        let available_width_for_system = terminal_width.saturating_sub(prefix_len as u16);
                        let options = Options::new(available_width_for_system as usize);
                        // Create the indentation string once
                        let indent = " ".repeat(prefix_len);

                        for line_str in text_content.lines() {
                            if first_line_of_message {
                                // First line gets the prefix
                                let wrapped_lines = textwrap::wrap(line_str, &options);
                                if wrapped_lines.is_empty() {
                                    // Handle empty line
                                    crossterm::execute!(out, Print(styled_sender_prefix.clone()), Print("\n"))?;
                                } else {
                                    // Print first wrapped line with prefix
                                    crossterm::execute!(out, Print(styled_sender_prefix.clone()), Print(&wrapped_lines[0]), Print("\n"))?;
                                    
                                    // Print remaining wrapped lines with proper indentation
                                    for wrapped_line in &wrapped_lines[1..] {
                                        crossterm::execute!(out, Print(indent.clone()), Print(wrapped_line), Print("\n"))?;
                                    }
                                }
                                first_line_of_message = false;
                            } else {
                                // Subsequent lines get wrapped with proper indentation
                                let wrapped_lines = textwrap::wrap(line_str, &options);
                                for wrapped_line in wrapped_lines {
                                    crossterm::execute!(out, Print(indent.clone()), Print(wrapped_line), Print("\n"))?;
                                }
                            }
                        }
                        
                        if text_content.is_empty() && first_line_of_message {
                            crossterm::execute!(out, Print(format!("{}\n", styled_sender_prefix.to_string().trim_end())))?;
                            first_line_of_message = false;
                        }
                    }
                    _ => {
                        let unsupported_msg = "[Unsupported content type for this sender]";
                        if first_line_of_message {
                            crossterm::execute!(out, Print(styled_sender_prefix.clone()), Print(unsupported_msg), Print("\n"))?;
                            first_line_of_message = false;
                        } else {
                            crossterm::execute!(out, Print(unsupported_msg), Print("\n"))?;
                        }
                    }
                }
            }
        }
        if !first_line_of_message || app_msg.parts.is_empty() {
            crossterm::execute!(out, Print("\n"))?; // Ensure a blank line after the message block
        }
    }
    
    out.flush() // Ensure all buffered output is written
}
