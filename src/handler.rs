use crate::{
    app::{App, AppMessage, MessageContent},
    display, llm_client, session_manager,
};
use crossterm::style::Stylize; // For .yellow(), .bold() etc.
use reqwest::Client;
use std::{
    io::{self, Write},
    path::Path,
    process::Command as OsCommand,
    sync::{Arc, Mutex},
};

pub async fn process_user_input(
    buffer: String,
    app_arc: Arc<Mutex<App>>,
    http_client: &Client,
    session_file_path: &Path,
) -> Result<(), Box<dyn std::error::Error>> {
    // 1. Create user AppMessage, display it, add to app.messages, add to llm_history
    let user_app_message = AppMessage {
        sender: "User".to_string(),
        parts: vec![MessageContent::Text(buffer.clone())],
    };
    display::print_formatted_message(&user_app_message)?;

    {
        // Lock scope for app update
        let mut app_locked = app_arc.lock().unwrap();
        app_locked.messages.push(user_app_message.clone());
        app_locked.add_user_message_to_llm_history(buffer.clone());
        if let Err(e) =
            session_manager::save_session(session_file_path, &app_locked.conversation_history_for_llm)
        {
            eprintln!("{}", format!("Error saving session: {}", e).red());
        }
    } // Lock released

    let mut max_tool_iterations = 5; // Prevent infinite tool call loops

    'tool_loop: loop {
        if max_tool_iterations == 0 {
            let err_msg_text = "Max tool iterations reached. Aborting sequence.".to_string();
            let err_msg = AppMessage {
                sender: "System".to_string(),
                parts: vec![MessageContent::Text(err_msg_text.clone())],
            };
            display::print_formatted_message(&err_msg)?;
            {
                // Lock scope
                let mut app_locked = app_arc.lock().unwrap();
                app_locked.messages.push(err_msg);
                // Optionally add this system error to LLM history if it should know
                // app_locked.add_tool_response_to_llm_history("system_error", err_msg_text);
            } // Lock released
            break 'tool_loop;
        }
        max_tool_iterations -= 1;

        let messages_for_api: Vec<llm_client::ChatMessage>;
        {
            // Lock scope for reading history
            let app_locked = app_arc.lock().unwrap();
            let mut constructed_messages = vec![llm_client::ChatMessage {
                role: "system".to_string(),
                content: llm_client::SYSTEM_PROMPT.to_string(),
            }];
            constructed_messages.extend_from_slice(&app_locked.conversation_history_for_llm);
            messages_for_api = constructed_messages;
        } // Lock released

        println!("{}", "AI is thinking...".italic().yellow());
        io::stdout().flush()?; // Ensure "thinking" message is displayed

        // Capture the start time for potential retry messages
        let llm_result = llm_client::call_llm_api(http_client, messages_for_api).await;

        match llm_result {
            Ok(response_content) => {
                let ai_app_message_for_display: AppMessage;
                let mut should_continue_tool_loop = false;
                {
                    // Scope for app_arc lock
                    let mut app_locked = app_arc.lock().unwrap();
                    // app_locked.llm_is_thinking = false; // Not strictly needed for CLI if "thinking" is just printed
                    app_locked.add_assistant_response_to_llm_history(response_content.clone());
                    ai_app_message_for_display =
                        app_locked.create_ai_app_message_from_raw(&response_content);
                    app_locked.messages.push(ai_app_message_for_display.clone());

                    if let Some(MessageContent::ToolCall(tool_call_action)) =
                        ai_app_message_for_display.parts.first()
                    {
                        // --- Tool Call Execution Logic ---
                        if tool_call_action.tool_name == "execute_command" {
                            let args = &tool_call_action.arguments;
                            let command_str_opt = args
                                .get("command")
                                .and_then(|v| v.as_str())
                                .map(String::from);
                            let cwd_opt = args.get("cwd").and_then(|v| v.as_str()).map(String::from);

                            if let Some(command_str) = command_str_opt {
                                drop(app_locked); // Release lock before blocking for confirmation

                                display::print_formatted_message(&ai_app_message_for_display)?;

                                println!(
                                    "{}",
                                    format!("\nAI requests to execute command: `{}`", command_str)
                                        .yellow()
                                        .bold()
                                );
                                if let Some(ref cwd) = cwd_opt {
                                    println!("{}", format!("In directory: `{}`", cwd).yellow());
                                }
                                print!("{}", "Confirm execution? (y/N): ".bold());
                                io::stdout().flush()?;

                                let mut confirmation_input = String::new();
                                io::stdin().read_line(&mut confirmation_input)?;

                                let tool_output_str: String;
                                if confirmation_input.trim().eq_ignore_ascii_case("y") {
                                    let mut os_cmd_process = OsCommand::new("sh");
                                    os_cmd_process.arg("-c").arg(&command_str);
                                    if let Some(cwd) = cwd_opt {
                                        os_cmd_process.current_dir(cwd);
                                    }

                                    match os_cmd_process.output() {
                                        Ok(output_val) => {
                                            let stdout =
                                                String::from_utf8_lossy(&output_val.stdout);
                                            let stderr =
                                                String::from_utf8_lossy(&output_val.stderr);
                                            tool_output_str = format!(
                                                "Command '{}' executed.\nStatus: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
                                                command_str, output_val.status, stdout, stderr
                                            );
                                        }
                                        Err(e) => {
                                            tool_output_str = format!(
                                                "Failed to execute command '{}': {}",
                                                command_str, e
                                            );
                                        }
                                    }
                                } else {
                                    tool_output_str = format!(
                                        "User denied execution of command: {}",
                                        command_str
                                    );
                                }

                                let tool_result_display_msg = AppMessage {
                                    sender: "Tool Execution".to_string(),
                                    parts: vec![MessageContent::Text(tool_output_str.clone())],
                                };
                                display::print_formatted_message(&tool_result_display_msg)?;

                                // Re-acquire lock to update history
                                let mut app_locked_again = app_arc.lock().unwrap();
                                app_locked_again.messages.push(tool_result_display_msg);
                                app_locked_again.add_tool_response_to_llm_history(
                                    &tool_call_action.tool_name,
                                    tool_output_str,
                                );
                                should_continue_tool_loop = true;
                            } else {
                                // Malformed arguments for execute_command
                                let err_text = "System: AI provided malformed arguments for execute_command tool.".to_string();
                                app_locked.add_tool_response_to_llm_history(
                                    &tool_call_action.tool_name,
                                    err_text.clone(),
                                );
                                let err_display_msg = AppMessage {
                                    sender: "System".to_string(),
                                    parts: vec![MessageContent::Text(err_text)],
                                };
                                app_locked.messages.push(err_display_msg.clone());
                                drop(app_locked); // Release lock
                                display::print_formatted_message(&err_display_msg)?;
                                should_continue_tool_loop = true;
                            }
                        } else {
                            // Handle other tools here if they exist, or inform user tool is not implemented
                            let tool_not_impl_text = format!(
                                "Tool '{}' is not implemented in this version.",
                                tool_call_action.tool_name
                            );
                            let tool_not_impl_msg = AppMessage {
                                sender: "System".to_string(),
                                parts: vec![MessageContent::Text(tool_not_impl_text.clone())],
                            };
                            app_locked.messages.push(tool_not_impl_msg.clone());
                            app_locked.add_tool_response_to_llm_history(
                                &tool_call_action.tool_name,
                                tool_not_impl_text,
                            );
                            drop(app_locked); // Release lock
                            display::print_formatted_message(&ai_app_message_for_display)?; // Show the AI's request
                            display::print_formatted_message(&tool_not_impl_msg)?; // Show the system message
                            should_continue_tool_loop = true; // Let LLM know about this
                        }
                    } else {
                         drop(app_locked); // Release lock if no tool call processing path took it
                    }
                } // Lock released here if not already manually dropped (e.g. after add_assistant_response...)

                if !should_continue_tool_loop && ai_app_message_for_display.sender == "AI" {
                     let is_execute_command_req = ai_app_message_for_display.parts.first()
                        .is_some_and(|part| matches!(part, MessageContent::ToolCall(tc) if tc.tool_name == "execute_command"));

                    if !is_execute_command_req { // Avoid double printing execute_command request
                        display::print_formatted_message(&ai_app_message_for_display)?;
                    }
                }

                { // Scope for final save in this iteration of tool_loop
                    let app_locked = app_arc.lock().unwrap();
                    if let Err(e) = session_manager::save_session(
                        session_file_path,
                        &app_locked.conversation_history_for_llm,
                    ) {
                        eprintln!("{}", format!("Error saving session: {}", e).red());
                    }
                } // Lock released

                if should_continue_tool_loop {
                    continue 'tool_loop;
                } else {
                    break 'tool_loop;
                }
            }
            Err(e) => {
                let system_error_msg = AppMessage {
                    sender: "System".to_string(),
                    parts: vec![MessageContent::Text(format!("Error from LLM: {}", e))],
                };
                display::print_formatted_message(&system_error_msg)?;
                { // Lock scope
                    app_arc.lock().unwrap().messages.push(system_error_msg);
                } // Lock released
                break 'tool_loop; // Break on LLM error
            }
        }
    } // End 'tool_loop

    Ok(())
}
