use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::time::Duration;
use tokio::time::sleep;

const POLLINATIONS_API_URL: &str = "https://text.pollinations.ai/openai/v1/chat/completions";
const MODEL_NAME: &str = "grok";

pub const SYSTEM_PROMPT: &str = include_str!("../prompts/system_prompt.txt");

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ChatMessage {
    pub role: String,
    pub content: String,
    // For full OpenAI compatibility, `tool_calls` (for assistant) and `tool_call_id` (for tool) would be needed.
    // Pollinations might be simpler, relying on content and role.
}

// Tool definition structures following OpenAI format
#[derive(Serialize, Debug, Clone)]
pub struct ToolParameter {
    #[serde(rename = "type")]
    pub param_type: String,
    pub description: String,
}

#[derive(Serialize, Debug, Clone)]
pub struct ToolParameters {
    #[serde(rename = "type")]
    pub param_type: String,
    pub properties: HashMap<String, ToolParameter>,
    pub required: Vec<String>,
    #[serde(rename = "additionalProperties")]
    pub additional_properties: bool,
}

#[derive(Serialize, Debug, Clone)]
pub struct ToolFunction {
    pub name: String,
    pub description: String,
    pub parameters: ToolParameters,
}

#[derive(Serialize, Debug, Clone)]
pub struct Tool {
    #[serde(rename = "type")]
    pub tool_type: String,
    pub function: ToolFunction,
}

#[derive(Serialize, Debug)]
pub struct ChatCompletionRequest {
    pub model: String,
    pub messages: Vec<ChatMessage>,
    pub stream: bool,
    pub tools: Option<Vec<Tool>>,
}

#[derive(Deserialize, Debug)]
struct ChatCompletionChoice {
    message: ChatMessage,
    // Could also include `tool_calls` here if the API populates it.
    // For now, assuming tool call JSON is in message.content for Pollinations.
}

#[derive(Deserialize, Debug)]
pub struct ChatCompletionResponse {
    choices: Vec<ChatCompletionChoice>,
}

#[derive(Debug, thiserror::Error)]
pub enum LLMError {
    #[error("HTTP request failed: {0}")]
    Network(#[from] reqwest::Error),
    #[error("API error: {0}")]
    ApiError(String),
    #[error("Failed to parse response: {0}")]
    Parse(String),
}

// Helper function to make a single API call attempt
async fn make_api_call_attempt(
    client: &Client,
    request_payload: &ChatCompletionRequest,
) -> Result<String, LLMError> {


    let response = client
        .post(POLLINATIONS_API_URL)
        .json(request_payload)
        .send()
        .await?;

    if response.status().is_success() {
        let chat_response = response.json::<ChatCompletionResponse>().await?;
        if let Some(choice) = chat_response.choices.first() {
            Ok(choice.message.content.clone())
        } else {
            Err(LLMError::Parse("No choices in response".to_string()))
        }
    } else {
        let status = response.status();
        let error_body = response
            .text()
            .await
            .unwrap_or_else(|_| "Could not retrieve error body".to_string());
        Err(LLMError::ApiError(format!(
            "API request failed with status {}: {}",
            status, error_body
        )))
    }
}

// Function to determine if an error is retryable
fn is_retryable_error(err: &LLMError) -> bool {
    match err {
        LLMError::Network(req_err) => {
            req_err.is_timeout() || req_err.is_connect() || req_err.is_request()
        }
        LLMError::ApiError(msg) => {
            // Retry on 5xx server errors, especially 502 Bad Gateway
            msg.contains("502 Bad Gateway")
                || msg.contains("Unable to reach the origin service")
                || msg.contains("500 Internal Server Error")
                || msg.contains("503 Service Unavailable")
                || msg.contains("504 Gateway Timeout")
        }
        _ => false,
    }
}

// Function to create tool definitions
pub fn create_tools() -> Vec<Tool> {
    vec![
        Tool {
            tool_type: "function".to_string(),
            function: ToolFunction {
                name: "read_file".to_string(),
                description: "Read the content of a specified file".to_string(),
                parameters: ToolParameters {
                    param_type: "object".to_string(),
                    properties: {
                        let mut props = HashMap::new();
                        props.insert(
                            "path".to_string(),
                            ToolParameter {
                                param_type: "string".to_string(),
                                description: "The full path to the file to read".to_string(),
                            },
                        );
                        props
                    },
                    required: vec!["path".to_string()],
                    additional_properties: false,
                },
            },
        },
        Tool {
            tool_type: "function".to_string(),
            function: ToolFunction {
                name: "write_file".to_string(),
                description: "Write or overwrite content to a specified file. Creates the file if it doesn't exist".to_string(),
                parameters: ToolParameters {
                    param_type: "object".to_string(),
                    properties: {
                        let mut props = HashMap::new();
                        props.insert(
                            "path".to_string(),
                            ToolParameter {
                                param_type: "string".to_string(),
                                description: "The full path to the file to write".to_string(),
                            },
                        );
                        props.insert(
                            "content".to_string(),
                            ToolParameter {
                                param_type: "string".to_string(),
                                description: "The content to write into the file".to_string(),
                            },
                        );
                        props
                    },
                    required: vec!["path".to_string(), "content".to_string()],
                    additional_properties: false,
                },
            },
        },
        Tool {
            tool_type: "function".to_string(),
            function: ToolFunction {
                name: "list_directory".to_string(),
                description: "List files and subdirectories in a specified directory".to_string(),
                parameters: ToolParameters {
                    param_type: "object".to_string(),
                    properties: {
                        let mut props = HashMap::new();
                        props.insert(
                            "path".to_string(),
                            ToolParameter {
                                param_type: "string".to_string(),
                                description: "The path to the directory".to_string(),
                            },
                        );
                        props.insert(
                            "recursive".to_string(),
                            ToolParameter {
                                param_type: "boolean".to_string(),
                                description: "Whether to list recursively. Defaults to false".to_string(),
                            },
                        );
                        props
                    },
                    required: vec!["path".to_string()],
                    additional_properties: false,
                },
            },
        },
        Tool {
            tool_type: "function".to_string(),
            function: ToolFunction {
                name: "execute_command".to_string(),
                description: "Execute a terminal command with configurable timeout. User confirmation will be sought before execution".to_string(),
                parameters: ToolParameters {
                    param_type: "object".to_string(),
                    properties: {
                        let mut props = HashMap::new();
                        props.insert(
                            "command".to_string(),
                            ToolParameter {
                                param_type: "string".to_string(),
                                description: "The command to execute (e.g., 'git status', 'cargo build')".to_string(),
                            },
                        );
                        props.insert(
                            "cwd".to_string(),
                            ToolParameter {
                                param_type: "string".to_string(),
                                description: "The current working directory for the command. Defaults to the TUI's current directory.".to_string(),
                            },
                        );
                        props.insert(
                            "timeout_seconds".to_string(),
                            ToolParameter {
                                param_type: "number".to_string(),
                                description: "Timeout in seconds for command execution. Defaults to 30 seconds.".to_string(),
                            },
                        );
                        props
                    },
                    required: vec!["command".to_string()],
                    additional_properties: false,
                },
            },
        },
    ]
}

pub async fn call_llm_api(
    client: &Client,
    messages_for_api_call: Vec<ChatMessage>,
) -> Result<String, LLMError> {
    let tools = create_tools();
    let request_payload = ChatCompletionRequest {
        model: MODEL_NAME.to_string(),
        messages: messages_for_api_call,
        stream: false,
        tools: Some(tools),
    };

    // Retry configuration
    let max_retries = 4;
    let retry_delays = [
        Duration::from_millis(200),
        Duration::from_millis(400),
        Duration::from_millis(800),
        Duration::from_millis(1600),
    ];

    // First attempt
    let mut result = make_api_call_attempt(client, &request_payload).await;

    // Retry loop
    let mut attempt = 0;
    while attempt < max_retries && result.is_err() {
        let err = result.as_ref().unwrap_err();

        if !is_retryable_error(err) {
            // If it's not a retryable error, break immediately
            break;
        }

        // Get the delay for this attempt
        let default_delay = Duration::from_secs(2);
        let delay = retry_delays.get(attempt).unwrap_or(&default_delay);

        // Log the retry attempt
        eprintln!(
            "API call failed with error: {}. Retrying in {:?}...",
            err, delay
        );

        // Wait before retrying
        sleep(*delay).await;

        // Make another attempt
        result = make_api_call_attempt(client, &request_payload).await;
        attempt += 1;
    }

    // If we've exhausted all retries and still have an error, add context to the error
    if result.is_err() && attempt > 0 {
        let err = result.unwrap_err();
        return Err(LLMError::ApiError(format!(
            "{} (after {} retries)",
            err, attempt
        )));
    }

    result
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub struct ToolCallAction {
    pub tool_name: String,
    pub arguments: Value, // Using Value for flexibility in arguments
                          // To align with OpenAI, this might also have an `id` from the assistant's `tool_calls`.
}

// Function to attempt parsing a string as a ToolCallAction
pub fn try_parse_tool_call(content: &str) -> Option<ToolCallAction> {
    let trimmed_content = content.trim();
    if trimmed_content.starts_with('{') && trimmed_content.ends_with('}') {
        serde_json::from_str::<ToolCallAction>(trimmed_content).ok()
    } else {
        None
    }
}
